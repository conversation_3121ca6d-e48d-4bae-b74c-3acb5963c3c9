<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sign In</title>
    <link rel="stylesheet" href="login.css">
    <script src="https://cdnjs.cloudflare.com/ajax/libs/lottie-web/5.7.8/lottie.min.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.6.8/firebase-app.js"></script>
    <script src="https://www.gstatic.com/firebasejs/8.6.8/firebase-auth.js"></script>
    <script src="loading-overlay.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600&display=swap" rel="stylesheet">
</head>
<body>
    <div class="login-container">
        <div class="login-heading">
            <h3>Welcome</h3>
            <p>Sign in to access your dashboard</p>
        </div>
        <form action="#" method="POST">
            <div class="input-group">
                <input id="email" name="email" type="email" autocomplete="email" required placeholder="Email address">
                <p id="emailError"></p>
            </div>
            <div class="input-group">
                <input id="password" name="password" type="password" autocomplete="current-password" required placeholder="Password">
                <p id="passwordError"></p>
            </div>
            <div class="remember-forgot">
                <div class="remember-me">
                    <input id="remember-me" name="remember-me" type="checkbox">
                    <label for="remember-me">Remember me</label>
                </div>
                <div class="forgot-password">
                    <a href="#" id="forgotPassword">Forgot password?</a>
                </div>
            </div>
            <div class="signin-button">
                <button type="submit">Sign in</button>
            </div>
        </form>
        <div class="request-access">
            <p>Don't have an account? <a href="signup.html">Sign up</a></p>
        </div>
    </div>
    <div id="loading-overlay">
        <div id="loading-animation" class="loading-animation"></div>
        <div class="loading-text">Signing in...</div>
    </div>
    <script src="login.js"></script>
    <footer class="fixed left-5 bottom-5" id="logo-footer">
        <img src="logosmall.png" alt="Logo" class="logo-img w-auto max-w-[120px]" id="footer-logo-img">
    </footer>

    <script>
        // Function to handle logo responsiveness
        function checkLogoOverlap() {
            const logo = document.getElementById('footer-logo-img');
            const footer = document.getElementById('logo-footer');
            const viewportWidth = window.innerWidth;

            // Automatically hide logo on very small screens
            if (viewportWidth <= 380) {
                footer.style.display = 'none';
                return;
            } else {
                footer.style.display = 'block';
            }

            // Adjust opacity and size based on screen size
            if (viewportWidth <= 480) {
                logo.style.opacity = '0.7';
                logo.style.maxWidth = '80px';
            } else if (viewportWidth <= 640) {
                logo.style.opacity = '0.8';
                logo.style.maxWidth = '100px';
            } else {
                logo.style.opacity = '0.9';
                logo.style.maxWidth = '120px';
            }
        }

        // Run on page load and window resize
        window.addEventListener('load', checkLogoOverlap);
        window.addEventListener('resize', checkLogoOverlap);
    </script>
</body>
</html>
